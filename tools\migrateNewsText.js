const { createClient } = require('@sanity/client')
require('dotenv').config()

// Configuration
const projectId = process.env.NEXT_PUBLIC_SANITY_PROJECT_ID || process.env.SANITY_PROJECT_ID
const dataset = process.env.NEXT_PUBLIC_SANITY_DATASET || process.env.SANITY_DATASET
const token = process.env.SANITY_TOKEN

if (!projectId || !dataset || !token) {
  console.error('Missing required environment variables:')
  console.error('- SANITY_PROJECT_ID or NEXT_PUBLIC_SANITY_PROJECT_ID')
  console.error('- SANITY_DATASET or NEXT_PUBLIC_SANITY_DATASET') 
  console.error('- SANITY_TOKEN')
  process.exit(1)
}

// Initialize Sanity client
const client = createClient({
  projectId,
  dataset,
  token,
  apiVersion: '2024-03-24',
  useCdn: false,
})

// Function to convert string to PortableText blocks
function stringToPortableText(text) {
  if (!text || typeof text !== 'string') {
    return []
  }
  
  // Split by line breaks and create paragraph blocks
  const paragraphs = text.split('\n').filter(p => p.trim().length > 0)
  
  return paragraphs.map(paragraph => ({
    _type: 'block',
    _key: Math.random().toString(36).substr(2, 9),
    style: 'normal',
    markDefs: [],
    children: [
      {
        _type: 'span',
        _key: Math.random().toString(36).substr(2, 9),
        text: paragraph.trim(),
        marks: []
      }
    ]
  }))
}

async function migrateNewsText() {
  try {
    console.log('🔍 Fetching news with string summaries...')
    
    // Fetch all news
    const news = await client.fetch(`
      *[_type == "news" && !(_id in path("drafts.**"))] {
        _id,
        _rev,
        title,
        summary
      }
    `)
    
    console.log(`📊 Found ${news.length} news articles`)
    
    // Filter news that have string summaries
    const newsToMigrate = news.filter(article => 
      article.summary && typeof article.summary === 'string'
    )
    
    console.log(`🔄 ${newsToMigrate.length} news articles need migration`)
    
    if (newsToMigrate.length === 0) {
      console.log('✅ No news articles need migration!')
      return
    }
    
    // Show what will be migrated
    console.log('\n📋 News articles to migrate:')
    newsToMigrate.forEach(article => {
      console.log(`- ${article.title}: "${article.summary.substring(0, 50)}${article.summary.length > 50 ? '...' : ''}"`)
    })
    
    // Ask for confirmation
    const readline = require('readline')
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    })
    
    const answer = await new Promise(resolve => {
      rl.question('\n❓ Do you want to proceed with the migration? (y/N): ', resolve)
    })
    rl.close()
    
    if (answer.toLowerCase() !== 'y' && answer.toLowerCase() !== 'yes') {
      console.log('❌ Migration cancelled')
      return
    }
    
    console.log('\n🚀 Starting migration...')
    
    // Migrate each news article
    for (const article of newsToMigrate) {
      try {
        const portableTextSummary = stringToPortableText(article.summary)
        
        console.log(`📝 Migrating ${article.title}...`)
        
        await client
          .patch(article._id)
          .set({ summary: portableTextSummary })
          .commit()
        
        console.log(`✅ Successfully migrated ${article.title}`)
      } catch (error) {
        console.error(`❌ Failed to migrate ${article.title}:`, error.message)
      }
    }
    
    console.log('\n🎉 Migration completed!')
    console.log('💡 You can now use the rich text editor in Sanity Studio for news summaries.')
    
  } catch (error) {
    console.error('❌ Migration failed:', error.message)
    process.exit(1)
  }
}

// Run the migration
migrateNewsText()
