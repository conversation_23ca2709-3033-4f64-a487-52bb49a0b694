// Client-side session tracking utility

export interface TrackingOptions {
  sessionId?: string
  page: string
  pageTitle?: string
  activityType?: string
  referrer?: string
  element?: string
  elementText?: string
  targetUrl?: string
  scrollDepth?: number
  timeOnPage?: number
  searchQuery?: string
  filterValues?: string[]
  formData?: {
    formId?: string
    fieldCount?: number
    success?: boolean
  }
  metadata?: any
}

class ClientSessionTracker {
  private sessionId: string | null = null
  private pageStartTime: number = Date.now()
  private lastScrollDepth: number = 0
  private isTracking: boolean = true

  constructor() {
    // Get session ID from localStorage or cookie
    this.sessionId = this.getStoredSessionId()
    
    // Set up automatic tracking
    this.setupAutoTracking()
  }

  private getStoredSessionId(): string | null {
    if (typeof window === 'undefined') return null
    
    try {
      return localStorage.getItem('qrp_session_id')
    } catch {
      return null
    }
  }

  private setStoredSessionId(sessionId: string): void {
    if (typeof window === 'undefined') return
    
    try {
      localStorage.setItem('qrp_session_id', sessionId)
    } catch {
      // Ignore localStorage errors
    }
  }

  private setupAutoTracking(): void {
    if (typeof window === 'undefined') return

    // Track page visibility changes
    document.addEventListener('visibilitychange', () => {
      if (document.hidden) {
        this.trackActivity({
          page: window.location.pathname,
          activityType: 'page_blur',
          timeOnPage: Math.floor((Date.now() - this.pageStartTime) / 1000)
        })
      } else {
        this.pageStartTime = Date.now()
        this.trackActivity({
          page: window.location.pathname,
          activityType: 'page_focus'
        })
      }
    })

    // Track scroll depth
    let scrollTimeout: NodeJS.Timeout
    window.addEventListener('scroll', () => {
      clearTimeout(scrollTimeout)
      scrollTimeout = setTimeout(() => {
        const scrollDepth = Math.round(
          (window.scrollY / (document.documentElement.scrollHeight - window.innerHeight)) * 100
        )
        
        // Only track significant scroll changes (every 25%)
        if (scrollDepth > this.lastScrollDepth && scrollDepth % 25 === 0) {
          this.lastScrollDepth = scrollDepth
          this.trackActivity({
            page: window.location.pathname,
            activityType: 'scroll',
            scrollDepth,
            timeOnPage: Math.floor((Date.now() - this.pageStartTime) / 1000)
          })
        }
      }, 500)
    })

    // Track clicks on important elements
    document.addEventListener('click', (event) => {
      const target = event.target as HTMLElement
      if (!target) return

      // Track clicks on links, buttons, and interactive elements
      if (target.tagName === 'A' || target.tagName === 'BUTTON' || target.onclick || target.getAttribute('role') === 'button') {
        this.trackActivity({
          page: window.location.pathname,
          activityType: 'click',
          element: target.tagName.toLowerCase(),
          elementText: target.textContent?.trim().substring(0, 100) || '',
          targetUrl: target.getAttribute('href') || undefined,
          timeOnPage: Math.floor((Date.now() - this.pageStartTime) / 1000)
        })
      }
    })

    // Track form submissions
    document.addEventListener('submit', (event) => {
      const form = event.target as HTMLFormElement
      if (!form) return

      const formData = new FormData(form)
      this.trackActivity({
        page: window.location.pathname,
        activityType: 'form_submit',
        formData: {
          formId: form.id || form.className || 'unknown',
          fieldCount: formData.entries ? Array.from(formData.entries()).length : 0,
          success: true // Will be updated if form validation fails
        },
        timeOnPage: Math.floor((Date.now() - this.pageStartTime) / 1000)
      })
    })

    // Track page unload
    window.addEventListener('beforeunload', () => {
      this.trackActivity({
        page: window.location.pathname,
        activityType: 'page_unload',
        timeOnPage: Math.floor((Date.now() - this.pageStartTime) / 1000)
      }, false) // Don't wait for response
    })
  }

  async trackPageView(page: string, pageTitle?: string): Promise<void> {
    this.pageStartTime = Date.now()
    this.lastScrollDepth = 0

    const options: TrackingOptions = {
      sessionId: this.sessionId,
      page,
      pageTitle,
      activityType: 'page_view',
      referrer: document.referrer || undefined
    }

    const result = await this.trackActivity(options)
    
    // If we got a new session ID, store it
    if (result?.sessionId && result.sessionId !== this.sessionId) {
      this.sessionId = result.sessionId
      this.setStoredSessionId(result.sessionId)
    }
  }

  async trackActivity(options: TrackingOptions, waitForResponse: boolean = true): Promise<any> {
    if (!this.isTracking || typeof window === 'undefined') return

    try {
      const requestOptions: RequestInit = {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          sessionId: this.sessionId,
          ...options
        })
      }

      if (waitForResponse) {
        const response = await fetch('/api/session/track', requestOptions)
        if (response.ok) {
          return await response.json()
        }
      } else {
        // Fire and forget for page unload events
        fetch('/api/session/track', requestOptions).catch(() => {
          // Ignore errors for fire-and-forget requests
        })
      }
    } catch (error) {
      console.error('Error tracking activity:', error)
    }
  }

  // Public methods for manual tracking
  trackClick(element: string, elementText?: string, targetUrl?: string): void {
    this.trackActivity({
      page: window.location.pathname,
      activityType: 'click',
      element,
      elementText,
      targetUrl,
      timeOnPage: Math.floor((Date.now() - this.pageStartTime) / 1000)
    })
  }

  trackSearch(query: string): void {
    this.trackActivity({
      page: window.location.pathname,
      activityType: 'search',
      searchQuery: query,
      timeOnPage: Math.floor((Date.now() - this.pageStartTime) / 1000)
    })
  }

  trackFilter(filterValues: string[]): void {
    this.trackActivity({
      page: window.location.pathname,
      activityType: 'filter',
      filterValues,
      timeOnPage: Math.floor((Date.now() - this.pageStartTime) / 1000)
    })
  }

  trackModalOpen(modalId: string): void {
    this.trackActivity({
      page: window.location.pathname,
      activityType: 'modal_open',
      element: modalId,
      timeOnPage: Math.floor((Date.now() - this.pageStartTime) / 1000)
    })
  }

  trackModalClose(modalId: string): void {
    this.trackActivity({
      page: window.location.pathname,
      activityType: 'modal_close',
      element: modalId,
      timeOnPage: Math.floor((Date.now() - this.pageStartTime) / 1000)
    })
  }

  // Disable tracking (for privacy compliance if needed)
  disableTracking(): void {
    this.isTracking = false
  }

  // Enable tracking
  enableTracking(): void {
    this.isTracking = true
  }
}

// Create singleton instance
let tracker: ClientSessionTracker | null = null

export function getSessionTracker(): ClientSessionTracker {
  if (!tracker && typeof window !== 'undefined') {
    tracker = new ClientSessionTracker()
  }
  return tracker!
}

// Convenience functions
export function trackPageView(page: string, pageTitle?: string): void {
  const tracker = getSessionTracker()
  if (tracker) {
    tracker.trackPageView(page, pageTitle)
  }
}

export function trackClick(element: string, elementText?: string, targetUrl?: string): void {
  const tracker = getSessionTracker()
  if (tracker) {
    tracker.trackClick(element, elementText, targetUrl)
  }
}

export function trackSearch(query: string): void {
  const tracker = getSessionTracker()
  if (tracker) {
    tracker.trackSearch(query)
  }
}

export function trackFilter(filterValues: string[]): void {
  const tracker = getSessionTracker()
  if (tracker) {
    tracker.trackFilter(filterValues)
  }
}

export function trackModalOpen(modalId: string): void {
  const tracker = getSessionTracker()
  if (tracker) {
    tracker.trackModalOpen(modalId)
  }
}

export function trackModalClose(modalId: string): void {
  const tracker = getSessionTracker()
  if (tracker) {
    tracker.trackModalClose(modalId)
  }
}
