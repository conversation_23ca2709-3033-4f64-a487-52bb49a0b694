const { createClient } = require('@sanity/client')
const { parse } = require('csv-parse')
const fs = require('fs')
require('dotenv').config()

// Initialize Sanity client
const client = createClient({
  projectId: process.env.SANITY_PROJECT_ID,
  dataset: process.env.SANITY_DATASET,
  token: process.env.SANITY_TOKEN,
  useCdn: false,
  apiVersion: '2023-01-01',
})

async function testConnection() {
  try {
    const result = await client.fetch('*[_type == "player"] | order(_createdAt desc) [0..2] {_id, nickname}')
    console.log('Connection test result:', result.length > 0 ? 'Success' : 'No players found')
    return true
  } catch (error) {
    console.error('Connection test failed:', error.message)
    return false
  }
}

async function cleanupIncorrectPlayers() {
  // Test connection first
  const connectionValid = await testConnection()
  if (!connectionValid) {
    console.error('Failed to establish connection to <PERSON><PERSON>. Please check your credentials.')
    process.exit(1)
  }

  console.log('Environment variables loaded:')
  console.log('Project ID:', process.env.SANITY_PROJECT_ID)
  console.log('Dataset:', process.env.SANITY_DATASET)

  // Step 1: Read CSV and build a map of players with their actual ELO
  const csvPlayers = new Map()
  
  console.log('Reading CSV file...')
  const fileContent = fs.readFileSync('players list.csv', 'utf-8')
  
  await new Promise((resolve, reject) => {
    parse(fileContent, {
      columns: false,
      skip_empty_lines: true,
      trim: true,
      from_line: 7, // Skip the first 6 header rows
    }, (err, records) => {
      if (err) {
        console.error('Error parsing CSV:', err)
        reject(err)
        return
      }

      console.log(`Found ${records.length} data rows in CSV`)

      records.forEach((record, index) => {
        const nickname = record[2] ? record[2].trim() : ''
        const eloStr = record[4] ? record[4].trim() : ''
        
        if (nickname && nickname.toLowerCase() !== 'nickname' && nickname !== '') {
          // Convert European decimal format (comma) to US format (dot)
          const eloValue = eloStr.replace(',', '.')
          const elo = parseFloat(eloValue)
          
          csvPlayers.set(nickname, {
            elo: isNaN(elo) ? 0 : elo,
            originalEloStr: eloStr
          })
        }
      })
      
      console.log(`Processed ${csvPlayers.size} players from CSV`)
      resolve(records)
    })
  })

  // Step 2: Fetch all players from Sanity with ELO = 1000
  console.log('\nFetching players with ELO = 1000 from Sanity...')
  const sanityPlayers = await client.fetch(
    `*[_type == "player" && elo == 1000]{_id, nickname, elo, _createdAt}`
  )
  
  console.log(`Found ${sanityPlayers.length} players with ELO = 1000 in Sanity`)

  // Step 3: Identify players that should be deleted
  const playersToDelete = []
  const playersToKeep = []
  
  sanityPlayers.forEach(sanityPlayer => {
    const csvData = csvPlayers.get(sanityPlayer.nickname)
    
    if (csvData) {
      // Player exists in CSV
      if (csvData.elo === 0 || isNaN(csvData.elo)) {
        // Player has 0 or invalid ELO in CSV, should be deleted
        playersToDelete.push({
          ...sanityPlayer,
          csvElo: csvData.elo,
          originalEloStr: csvData.originalEloStr
        })
      } else {
        // Player has valid ELO in CSV, should be kept (might be legitimate 1000 ELO)
        playersToKeep.push({
          ...sanityPlayer,
          csvElo: csvData.elo
        })
      }
    } else {
      // Player doesn't exist in CSV at all, might be manually added
      playersToKeep.push({
        ...sanityPlayer,
        csvElo: 'Not found in CSV'
      })
    }
  })

  console.log(`\nAnalysis results:`)
  console.log(`Players to DELETE (had 0/invalid ELO in CSV): ${playersToDelete.length}`)
  console.log(`Players to KEEP (legitimate 1000 ELO or not in CSV): ${playersToKeep.length}`)

  // Step 4: Show details and ask for confirmation
  if (playersToDelete.length > 0) {
    console.log('\nPlayers that will be DELETED (first 10):')
    playersToDelete.slice(0, 10).forEach(player => {
      console.log(`- ${player.nickname} (CSV ELO: ${player.originalEloStr})`)
    })
    
    if (playersToDelete.length > 10) {
      console.log(`... and ${playersToDelete.length - 10} more`)
    }
  }

  if (playersToKeep.length > 0) {
    console.log('\nPlayers that will be KEPT (first 10):')
    playersToKeep.slice(0, 10).forEach(player => {
      console.log(`- ${player.nickname} (CSV ELO: ${player.csvElo})`)
    })
    
    if (playersToKeep.length > 10) {
      console.log(`... and ${playersToKeep.length - 10} more`)
    }
  }

  // Step 5: Perform deletion (with safety check)
  if (playersToDelete.length === 0) {
    console.log('\nNo players need to be deleted. Cleanup complete!')
    return
  }

  console.log(`\n⚠️  WARNING: This will DELETE ${playersToDelete.length} players from Sanity!`)
  console.log('These are players that had 0 or invalid ELO in the CSV but were imported with 1000 ELO.')
  
  // For safety, require manual confirmation by uncommenting the deletion code
  console.log('\n🔒 SAFETY LOCK: To proceed with deletion, uncomment the deletion code in the script.')
  console.log('This prevents accidental deletions.')
  

  // UNCOMMENT THIS SECTION TO ACTUALLY PERFORM DELETIONS
  console.log('\nProceeding with deletion...')
  
  let deletedCount = 0
  const BATCH_SIZE = 5
  
  for (let i = 0; i < playersToDelete.length; i += BATCH_SIZE) {
    const batch = playersToDelete.slice(i, i + BATCH_SIZE)
    
    try {
      await Promise.all(batch.map(async (player) => {
        await client.delete(player._id)
        deletedCount++
        console.log(`Deleted: ${player.nickname} (${deletedCount}/${playersToDelete.length})`)
      }))
    } catch (error) {
      console.error(`Error deleting batch starting at index ${i}:`, error.message)
    }
  }
  
  console.log(`\n✅ Successfully deleted ${deletedCount} players`)
  
  console.log('\n📋 Summary:')
  console.log(`- Total players with ELO 1000 in Sanity: ${sanityPlayers.length}`)
  console.log(`- Players identified for deletion: ${playersToDelete.length}`)
  console.log(`- Players to keep: ${playersToKeep.length}`)
}

// Run the cleanup
cleanupIncorrectPlayers().catch(error => {
  console.error('Fatal error:', error)
  process.exit(1)
})
