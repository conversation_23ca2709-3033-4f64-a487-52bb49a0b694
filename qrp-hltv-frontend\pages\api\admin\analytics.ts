import type { NextApiRequest, NextApiResponse } from 'next'
import { verifyAdminAccess } from '../../../lib/roleUtils'
import { getActiveSessionsCount, getRecentSessions, getRecentActivities } from '../../../lib/sessionUtils'

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'GET') {
    return res.status(405).json({ message: 'Method not allowed' })
  }

  try {
    // Check admin access
    const { playerId, discordTag } = req.query
    
    const adminCheck = await verifyAdminAccess(
      playerId as string,
      discordTag as string
    )

    if (!adminCheck.hasAccess) {
      return res.status(403).json({ 
        message: 'Access denied. Admin privileges required.',
        hasAccess: false
      })
    }

    // Get analytics data
    const [activeSessionsCount, recentSessions, recentActivities] = await Promise.all([
      getActiveSessionsCount(),
      getRecentSessions(50),
      getRecentActivities(100)
    ])

    // Calculate additional metrics
    const totalSessions = recentSessions.length
    const activeSessions = recentSessions.filter(s => s.isActive).length
    
    // Calculate average session duration for completed sessions
    const completedSessions = recentSessions.filter(s => s.duration && s.duration > 0)
    const avgSessionDuration = completedSessions.length > 0
      ? Math.round(completedSessions.reduce((sum, s) => sum + (s.duration || 0), 0) / completedSessions.length)
      : 0

    // Get top pages
    const pageViews = recentActivities.filter(a => a.activityType === 'page_view')
    const pageStats = pageViews.reduce((acc, activity) => {
      acc[activity.page] = (acc[activity.page] || 0) + 1
      return acc
    }, {} as Record<string, number>)
    
    const topPages = Object.entries(pageStats)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([page, views]) => ({ page, views }))

    // Get country distribution
    const countryStats = recentSessions.reduce((acc, session) => {
      const country = session.country || 'Unknown'
      acc[country] = (acc[country] || 0) + 1
      return acc
    }, {} as Record<string, number>)
    
    const topCountries = Object.entries(countryStats)
      .sort(([,a], [,b]) => b - a)
      .slice(0, 10)
      .map(([country, sessions]) => ({ country, sessions }))

    // Get recent activity types
    const activityTypeStats = recentActivities.reduce((acc, activity) => {
      acc[activity.activityType] = (acc[activity.activityType] || 0) + 1
      return acc
    }, {} as Record<string, number>)

    const response = {
      success: true,
      hasAccess: true,
      adminUser: {
        nickname: adminCheck.player?.nickname,
        role: adminCheck.role
      },
      metrics: {
        activeSessionsCount,
        totalSessions,
        activeSessions,
        avgSessionDuration,
        totalActivities: recentActivities.length
      },
      sessions: recentSessions,
      activities: recentActivities.slice(0, 50), // Limit activities for performance
      analytics: {
        topPages,
        topCountries,
        activityTypeStats
      },
      timestamp: new Date().toISOString()
    }

    return res.status(200).json(response)

  } catch (error) {
    console.error('Error fetching analytics:', error)
    return res.status(500).json({ 
      success: false,
      message: 'Internal server error' 
    })
  }
}
