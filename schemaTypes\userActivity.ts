import {defineField, defineType} from 'sanity'

export default defineType({
  name: 'userActivity',
  title: 'User Activity',
  type: 'document',
  fields: [
    defineField({
      name: 'sessionId',
      title: 'Session ID',
      type: 'string',
      validation: (Rule) => Rule.required(),
      description: 'Reference to the user session',
    }),
    defineField({
      name: 'timestamp',
      title: 'Timestamp',
      type: 'datetime',
      validation: (Rule) => Rule.required(),
      description: 'When the activity occurred',
    }),
    defineField({
      name: 'activityType',
      title: 'Activity Type',
      type: 'string',
      options: {
        list: [
          { title: 'Page View', value: 'page_view' },
          { title: 'Click', value: 'click' },
          { title: 'Scroll', value: 'scroll' },
          { title: 'Form Submit', value: 'form_submit' },
          { title: 'Modal Open', value: 'modal_open' },
          { title: 'Modal Close', value: 'modal_close' },
          { title: 'Search', value: 'search' },
          { title: 'Filter', value: 'filter' },
          { title: 'Download', value: 'download' },
          { title: 'External Link', value: 'external_link' },
          { title: 'Session Start', value: 'session_start' },
          { title: 'Session End', value: 'session_end' }
        ]
      },
      validation: (Rule) => Rule.required(),
      description: 'Type of user activity',
    }),
    defineField({
      name: 'page',
      title: 'Page',
      type: 'string',
      validation: (Rule) => Rule.required(),
      description: 'Page URL or path where activity occurred',
    }),
    defineField({
      name: 'pageTitle',
      title: 'Page Title',
      type: 'string',
      description: 'Title of the page where activity occurred',
    }),
    defineField({
      name: 'element',
      title: 'Element',
      type: 'string',
      description: 'Element that was interacted with (for clicks, etc.)',
    }),
    defineField({
      name: 'elementText',
      title: 'Element Text',
      type: 'string',
      description: 'Text content of the element that was interacted with',
    }),
    defineField({
      name: 'targetUrl',
      title: 'Target URL',
      type: 'string',
      description: 'URL that was navigated to (for links, etc.)',
    }),
    defineField({
      name: 'scrollDepth',
      title: 'Scroll Depth',
      type: 'number',
      description: 'Percentage of page scrolled (for scroll events)',
    }),
    defineField({
      name: 'timeOnPage',
      title: 'Time on Page (seconds)',
      type: 'number',
      description: 'Time spent on the page before this activity',
    }),
    defineField({
      name: 'searchQuery',
      title: 'Search Query',
      type: 'string',
      description: 'Search term used (for search activities)',
    }),
    defineField({
      name: 'filterValues',
      title: 'Filter Values',
      type: 'array',
      of: [{ type: 'string' }],
      description: 'Filter values applied (for filter activities)',
    }),
    defineField({
      name: 'formData',
      title: 'Form Data',
      type: 'object',
      fields: [
        defineField({
          name: 'formId',
          title: 'Form ID',
          type: 'string',
        }),
        defineField({
          name: 'fieldCount',
          title: 'Field Count',
          type: 'number',
        }),
        defineField({
          name: 'success',
          title: 'Success',
          type: 'boolean',
        }),
      ],
      description: 'Form submission data (for form_submit activities)',
    }),
    defineField({
      name: 'metadata',
      title: 'Additional Metadata',
      type: 'object',
      fields: [
        defineField({
          name: 'data',
          title: 'Data',
          type: 'text',
          description: 'Additional activity-specific data as JSON string',
        }),
      ],
      description: 'Additional activity-specific data',
    }),
  ],
  preview: {
    select: {
      title: 'activityType',
      subtitle: 'page',
      description: 'timestamp',
    },
    prepare(selection) {
      const {title, subtitle, description} = selection
      const activityTypeLabels: Record<string, string> = {
        page_view: '👁️ Page View',
        click: '👆 Click',
        scroll: '📜 Scroll',
        form_submit: '📝 Form Submit',
        modal_open: '🔍 Modal Open',
        modal_close: '❌ Modal Close',
        search: '🔍 Search',
        filter: '🔽 Filter',
        download: '⬇️ Download',
        external_link: '🔗 External Link',
        session_start: '🟢 Session Start',
        session_end: '🔴 Session End'
      }
      
      return {
        title: activityTypeLabels[title] || title,
        subtitle: subtitle,
        description: description ? new Date(description).toLocaleString() : '',
      }
    },
  },
  orderings: [
    {
      title: 'Timestamp (Newest First)',
      name: 'timestampDesc',
      by: [
        {field: 'timestamp', direction: 'desc'}
      ]
    },
    {
      title: 'Session ID',
      name: 'sessionId',
      by: [
        {field: 'sessionId', direction: 'asc'},
        {field: 'timestamp', direction: 'asc'}
      ]
    }
  ]
})
