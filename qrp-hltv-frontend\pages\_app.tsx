import '../styles/globals.css'
import type { AppProps } from 'next/app'
import { LanguageProvider } from '../lib/LanguageContext'
import Layout from '../components/Layout'
import { useRouter } from 'next/router'
import { useEffect } from 'react'
import { trackPageView } from '../lib/clientSessionTracker'

export default function App({ Component, pageProps }: AppProps) {
  const router = useRouter()

  // Track page views
  useEffect(() => {
    const handleRouteChange = (url: string) => {
      // Get page title from document or generate from URL
      const pageTitle = document.title || `QRP HLTV - ${url.replace('/', '').replace(/^\w/, c => c.toUpperCase())}`
      trackPageView(url, pageTitle)
    }

    // Track initial page load
    handleRouteChange(router.asPath)

    // Track route changes
    router.events.on('routeChangeComplete', handleRouteChange)

    return () => {
      router.events.off('routeChangeComplete', handleRouteChange)
    }
  }, [router])

  // Pages that should not use the standard layout
  const pagesWithoutLayout = ['/not_allowed']
  const shouldUseLayout = !pagesWithoutLayout.includes(router.pathname)

  // Determine current page for header highlighting
  const getCurrentPage = () => {
    const path = router.pathname
    if (path === '/news') return 'news'
    if (path === '/tournaments') return 'tournaments'
    if (path === '/players') return 'players'
    if (path === '/teams') return 'teams'
    if (path === '/partners') return 'partners'
    if (path === '/contact') return 'contact'
    return undefined
  }

  return (
    <LanguageProvider>
      {shouldUseLayout ? (
        <Layout currentPage={getCurrentPage()}>
          <Component {...pageProps} />
        </Layout>
      ) : (
        <Component {...pageProps} />
      )}
    </LanguageProvider>
  )
}