import { v4 as uuidv4 } from 'uuid'
import { sanityWrite } from './sanity'

export interface SessionData {
  sessionId: string
  ipAddress: string
  userAgent: string
  startTime: string
  lastActivity: string
  endTime?: string
  isActive: boolean
  country?: string
  countryCode?: string
  region?: string
  city?: string
  timezone?: string
  isp?: string
  pageViews: number
  duration?: number
  referrer?: string
  entryPage: string
  exitPage?: string
}

export interface ActivityData {
  sessionId: string
  timestamp: string
  activityType: string
  page: string
  pageTitle?: string
  element?: string
  elementText?: string
  targetUrl?: string
  scrollDepth?: number
  timeOnPage?: number
  searchQuery?: string
  filterValues?: string[]
  formData?: {
    formId?: string
    fieldCount?: number
    success?: boolean
  }
  metadata?: any
}

export interface LocationData {
  country?: string
  countryCode?: string
  region?: string
  city?: string
  timezone?: string
  isp?: string
}

/**
 * Get location data from IP address using ipapi.co
 */
export async function getLocationFromIP(ipAddress: string): Promise<LocationData> {
  try {
    // Skip location lookup for localhost/private IPs
    if (ipAddress === '127.0.0.1' || ipAddress === '::1' || ipAddress.startsWith('192.168.') || ipAddress.startsWith('10.') || ipAddress.startsWith('172.')) {
      return {
        country: 'Local',
        countryCode: 'LOCAL',
        region: 'Local',
        city: 'Local',
        timezone: 'Local',
        isp: 'Local Network'
      }
    }

    const response = await fetch(`https://ipapi.co/${ipAddress}/json/`)
    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }
    
    const data = await response.json()
    
    return {
      country: data.country_name,
      countryCode: data.country_code,
      region: data.region,
      city: data.city,
      timezone: data.timezone,
      isp: data.org
    }
  } catch (error) {
    console.error('Error getting location from IP:', error)
    return {}
  }
}

/**
 * Create a new user session
 */
export async function createSession(
  ipAddress: string,
  userAgent: string,
  entryPage: string,
  referrer?: string
): Promise<string> {
  const sessionId = uuidv4()
  const now = new Date().toISOString()
  
  try {
    // Get location data
    const locationData = await getLocationFromIP(ipAddress)
    
    const sessionData: SessionData = {
      sessionId,
      ipAddress,
      userAgent,
      startTime: now,
      lastActivity: now,
      isActive: true,
      pageViews: 1,
      entryPage,
      referrer,
      ...locationData
    }
    
    // Create session document in Sanity
    await sanityWrite.create({
      _type: 'userSession',
      ...sessionData
    })
    
    // Log session start activity
    await logActivity({
      sessionId,
      timestamp: now,
      activityType: 'session_start',
      page: entryPage,
      pageTitle: 'Session Started'
    })
    
    return sessionId
  } catch (error) {
    console.error('Error creating session:', error)
    throw error
  }
}

/**
 * Update session activity
 */
export async function updateSessionActivity(
  sessionId: string,
  page: string,
  pageTitle?: string
): Promise<void> {
  const now = new Date().toISOString()
  
  try {
    // Update session last activity and page views
    await sanityWrite
      .patch(sessionId)
      .set({
        lastActivity: now,
        exitPage: page
      })
      .inc({ pageViews: 1 })
      .commit()
    
    // Log page view activity
    await logActivity({
      sessionId,
      timestamp: now,
      activityType: 'page_view',
      page,
      pageTitle
    })
  } catch (error) {
    console.error('Error updating session activity:', error)
  }
}

/**
 * End a user session
 */
export async function endSession(sessionId: string): Promise<void> {
  const now = new Date().toISOString()
  
  try {
    // Get session to calculate duration
    const session = await sanityWrite.fetch(
      `*[_type == "userSession" && sessionId == $sessionId][0]`,
      { sessionId }
    )
    
    if (session) {
      const startTime = new Date(session.startTime)
      const endTime = new Date(now)
      const duration = Math.floor((endTime.getTime() - startTime.getTime()) / 1000)
      
      // Update session
      await sanityWrite
        .patch(session._id)
        .set({
          endTime: now,
          isActive: false,
          duration
        })
        .commit()
      
      // Log session end activity
      await logActivity({
        sessionId,
        timestamp: now,
        activityType: 'session_end',
        page: session.exitPage || session.entryPage,
        pageTitle: 'Session Ended',
        timeOnPage: duration
      })
    }
  } catch (error) {
    console.error('Error ending session:', error)
  }
}

/**
 * Log user activity
 */
export async function logActivity(activityData: ActivityData): Promise<void> {
  try {
    await sanityWrite.create({
      _type: 'userActivity',
      ...activityData
    })
  } catch (error) {
    console.error('Error logging activity:', error)
  }
}

/**
 * Get active sessions count
 */
export async function getActiveSessionsCount(): Promise<number> {
  try {
    const count = await sanityWrite.fetch(
      `count(*[_type == "userSession" && isActive == true && lastActivity > $cutoff])`,
      { cutoff: new Date(Date.now() - 30 * 60 * 1000).toISOString() } // 30 minutes ago
    )
    return count || 0
  } catch (error) {
    console.error('Error getting active sessions count:', error)
    return 0
  }
}

/**
 * Get recent sessions for admin dashboard
 */
export async function getRecentSessions(limit: number = 50): Promise<SessionData[]> {
  try {
    const sessions = await sanityWrite.fetch(
      `*[_type == "userSession"] | order(lastActivity desc)[0...$limit]{
        sessionId,
        ipAddress,
        userAgent,
        startTime,
        lastActivity,
        endTime,
        isActive,
        country,
        countryCode,
        region,
        city,
        timezone,
        isp,
        pageViews,
        duration,
        referrer,
        entryPage,
        exitPage
      }`,
      { limit: limit - 1 }
    )
    return sessions || []
  } catch (error) {
    console.error('Error getting recent sessions:', error)
    return []
  }
}

/**
 * Get recent activities for admin dashboard
 */
export async function getRecentActivities(limit: number = 100): Promise<ActivityData[]> {
  try {
    const activities = await sanityWrite.fetch(
      `*[_type == "userActivity"] | order(timestamp desc)[0...$limit]{
        sessionId,
        timestamp,
        activityType,
        page,
        pageTitle,
        element,
        elementText,
        targetUrl,
        scrollDepth,
        timeOnPage,
        searchQuery,
        filterValues,
        formData,
        metadata
      }`,
      { limit: limit - 1 }
    )
    return activities || []
  } catch (error) {
    console.error('Error getting recent activities:', error)
    return []
  }
}
