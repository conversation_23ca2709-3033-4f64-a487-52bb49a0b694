import {defineField, defineType} from 'sanity'

export default defineType({
  name: 'tournament',
  title: 'Tournament',
  type: 'document',
  fields: [
    defineField({
      name: 'name',
      title: 'Tournament Name',
      type: 'string',
      validation: (Rule) => Rule.required(),
    }),
    defineField({
      name: 'qualDate',
      title: 'Qualifiers Date',
      type: 'datetime',
    }),
    defineField({
      name: 'tandemsDate',
      title: 'Tandems Date',
      type: 'datetime',
    }),
    defineField({
      name: 'status',
      title: 'Status',
      type: 'string',
      options: {
        list: [
          {title: 'Upcoming', value: 'Upcoming'},
          {title: 'Ongoing', value: 'Ongoing'},
          {title: 'Finished', value: 'Finished'},
        ],
      },
      validation: (Rule) => Rule.required(),
      initialValue: 'Upcoming',
    }),
    defineField({
      name: 'photo',
      title: 'Photo',
      type: 'image',
      options: {
        hotspot: true,
      },
    }),
    defineField({
      name: 'video',
      title: 'Video',
      type: 'file',
      description: 'For best performance, keep video files under 5MB. Use WebM or MP4 format with H.264 encoding.',
      options: {
        accept: 'video/*'
      },
      validation: (Rule) => Rule.custom((file) => {
        if (!file) return true // Video is optional

        // Note: File size validation in Sanity is limited as the file object
        // doesn't contain size information at validation time
        return true
      })
    }),
    defineField({
      name: 'description',
      title: 'Description',
      type: 'array',
      of: [{ type: 'block' }],
    }),
  ],
  preview: {
    select: {
      title: 'name',
      subtitle: 'status',
      media: 'photo',
    },
    prepare(selection) {
      const {title, subtitle, media} = selection
      return {
        title: title,
        subtitle: `Status: ${subtitle}`,
        media: media,
      }
    },
  },
})
