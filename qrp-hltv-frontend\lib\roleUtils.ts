import { sanity } from './sanity'

export type UserRole = 'creator' | 'support' | null

export interface PlayerWithRole {
  _id: string
  nickname: string
  role?: UserRole
}

/**
 * Check if a player has admin privileges (creator or support role)
 */
export function hasAdminRole(role?: UserRole): boolean {
  return role === 'creator' || role === 'support'
}

/**
 * Check if a player has creator role (highest privilege)
 */
export function hasCreatorRole(role?: UserRole): boolean {
  return role === 'creator'
}

/**
 * Get player by Discord tag and check their role
 */
export async function getPlayerByDiscord(discordTag: string): Promise<PlayerWithRole | null> {
  try {
    const player = await sanity.fetch(
      `*[_type == "player" && discord == $discord][0]{
        _id,
        nickname,
        role
      }`,
      { discord: discordTag }
    )
    return player || null
  } catch (error) {
    console.error('Error fetching player by Discord:', error)
    return null
  }
}

/**
 * Get player by ID and check their role
 */
export async function getPlayerById(playerId: string): Promise<PlayerWithRole | null> {
  try {
    const player = await sanity.fetch(
      `*[_type == "player" && _id == $id][0]{
        _id,
        nickname,
        role
      }`,
      { id: playerId }
    )
    return player || null
  } catch (error) {
    console.error('Error fetching player by ID:', error)
    return null
  }
}

/**
 * Get all players with admin roles
 */
export async function getAdminPlayers(): Promise<PlayerWithRole[]> {
  try {
    const players = await sanity.fetch(
      `*[_type == "player" && role in ["creator", "support"]]{
        _id,
        nickname,
        role
      } | order(role desc, nickname asc)`
    )
    return players || []
  } catch (error) {
    console.error('Error fetching admin players:', error)
    return []
  }
}

/**
 * Verify admin access for API routes
 */
export async function verifyAdminAccess(playerId?: string, discordTag?: string): Promise<{
  hasAccess: boolean
  player?: PlayerWithRole
  role?: UserRole
}> {
  try {
    let player: PlayerWithRole | null = null
    
    if (playerId) {
      player = await getPlayerById(playerId)
    } else if (discordTag) {
      player = await getPlayerByDiscord(discordTag)
    }
    
    if (!player) {
      return { hasAccess: false }
    }
    
    const hasAccess = hasAdminRole(player.role)
    
    return {
      hasAccess,
      player,
      role: player.role
    }
  } catch (error) {
    console.error('Error verifying admin access:', error)
    return { hasAccess: false }
  }
}
