import {defineField, defineType} from 'sanity'

export default defineType({
  name: 'userSession',
  title: 'User Session',
  type: 'document',
  fields: [
    defineField({
      name: 'sessionId',
      title: 'Session ID',
      type: 'string',
      validation: (Rule) => Rule.required(),
      description: 'Unique identifier for the user session',
    }),
    defineField({
      name: 'ipAddress',
      title: 'IP Address',
      type: 'string',
      validation: (Rule) => Rule.required(),
      description: 'User IP address',
    }),
    defineField({
      name: 'userAgent',
      title: 'User Agent',
      type: 'text',
      description: 'Browser and device information',
    }),
    defineField({
      name: 'startTime',
      title: 'Session Start Time',
      type: 'datetime',
      validation: (Rule) => Rule.required(),
      description: 'When the session started',
    }),
    defineField({
      name: 'lastActivity',
      title: 'Last Activity',
      type: 'datetime',
      validation: (Rule) => Rule.required(),
      description: 'Last time user was active',
    }),
    defineField({
      name: 'endTime',
      title: 'Session End Time',
      type: 'datetime',
      description: 'When the session ended (null if still active)',
    }),
    defineField({
      name: 'isActive',
      title: 'Is Active',
      type: 'boolean',
      initialValue: true,
      description: 'Whether the session is currently active',
    }),
    defineField({
      name: 'country',
      title: 'Country',
      type: 'string',
      description: 'Country based on IP geolocation',
    }),
    defineField({
      name: 'countryCode',
      title: 'Country Code',
      type: 'string',
      description: 'ISO country code',
    }),
    defineField({
      name: 'region',
      title: 'Region',
      type: 'string',
      description: 'Region/state based on IP geolocation',
    }),
    defineField({
      name: 'city',
      title: 'City',
      type: 'string',
      description: 'City based on IP geolocation',
    }),
    defineField({
      name: 'timezone',
      title: 'Timezone',
      type: 'string',
      description: 'User timezone based on IP geolocation',
    }),
    defineField({
      name: 'isp',
      title: 'ISP',
      type: 'string',
      description: 'Internet Service Provider',
    }),
    defineField({
      name: 'pageViews',
      title: 'Page Views',
      type: 'number',
      initialValue: 0,
      description: 'Total number of pages viewed in this session',
    }),
    defineField({
      name: 'duration',
      title: 'Session Duration (seconds)',
      type: 'number',
      description: 'Total session duration in seconds',
    }),
    defineField({
      name: 'referrer',
      title: 'Referrer',
      type: 'string',
      description: 'Page that referred the user to the site',
    }),
    defineField({
      name: 'entryPage',
      title: 'Entry Page',
      type: 'string',
      description: 'First page visited in the session',
    }),
    defineField({
      name: 'exitPage',
      title: 'Exit Page',
      type: 'string',
      description: 'Last page visited in the session',
    }),
  ],
  preview: {
    select: {
      title: 'sessionId',
      subtitle: 'ipAddress',
      description: 'city',
    },
    prepare(selection) {
      const {title, subtitle, description} = selection
      return {
        title: `Session: ${title?.substring(0, 8)}...`,
        subtitle: `IP: ${subtitle}`,
        description: description ? `Location: ${description}` : 'Location unknown',
      }
    },
  },
  orderings: [
    {
      title: 'Start Time (Newest First)',
      name: 'startTimeDesc',
      by: [
        {field: 'startTime', direction: 'desc'}
      ]
    },
    {
      title: 'Last Activity (Most Recent)',
      name: 'lastActivityDesc',
      by: [
        {field: 'lastActivity', direction: 'desc'}
      ]
    }
  ]
})
