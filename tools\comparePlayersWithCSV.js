const { createClient } = require('@sanity/client')
const fs = require('fs')
const { parse } = require('csv-parse')
require('dotenv').config()

// Initialize Sanity client
const client = createClient({
  projectId: process.env.NEXT_PUBLIC_SANITY_PROJECT_ID || process.env.SANITY_PROJECT_ID,
  dataset: process.env.NEXT_PUBLIC_SANITY_DATASET || process.env.SANITY_DATASET,
  token: process.env.SANITY_TOKEN,
  apiVersion: '2024-03-24',
  useCdn: false,
})

// Configuration
const CSV_FILE_PATH = 'players list.csv' // Update this path if needed
const NICKNAME_COLUMN_INDEX = 2 // Column C (0-indexed), which contains the nicknames

async function fetchSanityPlayers() {
  try {
    console.log('Fetching players from Sanity CMS...')
    const players = await client.fetch(`
      *[_type == "player" && !(_id in path("drafts.**"))] {
        _id,
        nickname,
        name,
        elo
      }
    `)
    console.log(`✓ Found ${players.length} players in Sanity CMS`)
    return players
  } catch (error) {
    console.error('Error fetching players from Sanity:', error.message)
    throw error
  }
}

async function readCSVPlayers(filePath) {
  return new Promise((resolve, reject) => {
    const players = []
    const parser = parse({
      skip_empty_lines: true,
      from_line: 7, // Start from line 7 to skip headers (based on CSV structure)
    })

    parser.on('readable', function() {
      let record
      while (record = parser.read()) {
        // Extract nickname from the specified column
        const nickname = record[NICKNAME_COLUMN_INDEX]
        const eloString = record[4] || '0' // ELO column
        const elo = parseFloat(eloString.replace(',', '.')) || 0

        // Skip players with ELO 0 and ensure nickname is valid
        if (nickname && nickname.trim() && nickname !== 'Nickname' && elo > 0) {
          players.push({
            nickname: nickname.trim(),
            // Also capture other data for reference
            name: record[3] || '', // Name column
            elo: elo, // Parsed ELO as number
            position: record[1] || '' // Position column
          })
        }
      }
    })

    parser.on('error', function(err) {
      console.error('Error parsing CSV:', err.message)
      reject(err)
    })

    parser.on('end', function() {
      console.log(`✓ Found ${players.length} active players in CSV file (ELO > 0)`)
      resolve(players)
    })

    // Read the file
    try {
      const fileContent = fs.readFileSync(filePath, 'utf8')
      parser.write(fileContent)
      parser.end()
    } catch (error) {
      console.error('Error reading CSV file:', error.message)
      reject(error)
    }
  })
}

function comparePlayerLists(sanityPlayers, csvPlayers) {
  // Create sets of nicknames for easy comparison
  const sanityNicknames = new Set(sanityPlayers.map(p => p.nickname.toLowerCase().trim()))
  const csvNicknames = new Set(csvPlayers.map(p => p.nickname.toLowerCase().trim()))

  // Find players in Sanity but not in CSV
  const inSanityNotInCSV = sanityPlayers.filter(player => 
    !csvNicknames.has(player.nickname.toLowerCase().trim())
  )

  // Find players in CSV but not in Sanity
  const inCSVNotInSanity = csvPlayers.filter(player => 
    !sanityNicknames.has(player.nickname.toLowerCase().trim())
  )

  return {
    inSanityNotInCSV,
    inCSVNotInSanity,
    totalSanity: sanityPlayers.length,
    totalCSV: csvPlayers.length,
    commonPlayers: sanityPlayers.length - inSanityNotInCSV.length
  }
}

function displayResults(comparison) {
  console.log('\n' + '='.repeat(80))
  console.log('                    PLAYER COMPARISON RESULTS')
  console.log('='.repeat(80))
  
  console.log(`\n📊 SUMMARY:`)
  console.log(`   • Total players in Sanity CMS: ${comparison.totalSanity}`)
  console.log(`   • Total players in CSV file (ELO > 0): ${comparison.totalCSV}`)
  console.log(`   • Players in both sources: ${comparison.commonPlayers}`)
  console.log(`   • Players only in Sanity: ${comparison.inSanityNotInCSV.length}`)
  console.log(`   • Players only in CSV: ${comparison.inCSVNotInSanity.length}`)
  console.log(`   ℹ️  Note: Players with ELO 0 in CSV are excluded from comparison`)

  if (comparison.inSanityNotInCSV.length > 0) {
    console.log(`\n🔍 PLAYERS IN SANITY CMS BUT NOT IN CSV (${comparison.inSanityNotInCSV.length}):`)
    console.log('-'.repeat(60))
    comparison.inSanityNotInCSV.forEach((player, index) => {
      console.log(`${(index + 1).toString().padStart(3)}. ${player.nickname}${player.name ? ` (${player.name})` : ''} - ELO: ${player.elo || 'N/A'}`)
    })
  }

  if (comparison.inCSVNotInSanity.length > 0) {
    console.log(`\n🔍 PLAYERS IN CSV BUT NOT IN SANITY CMS (${comparison.inCSVNotInSanity.length}):`)
    console.log('-'.repeat(60))
    comparison.inCSVNotInSanity.forEach((player, index) => {
      console.log(`${(index + 1).toString().padStart(3)}. ${player.nickname}${player.name ? ` (${player.name})` : ''} - ELO: ${player.elo || 'N/A'} - Position: ${player.position || 'N/A'}`)
    })
  }

  if (comparison.inSanityNotInCSV.length === 0 && comparison.inCSVNotInSanity.length === 0) {
    console.log('\n✅ PERFECT MATCH! All players are synchronized between Sanity CMS and CSV file.')
  }

  console.log('\n' + '='.repeat(80))
}

async function main() {
  try {
    console.log('🚀 Starting player comparison between Sanity CMS and CSV file...\n')

    // Check if CSV file exists
    if (!fs.existsSync(CSV_FILE_PATH)) {
      throw new Error(`CSV file not found: ${CSV_FILE_PATH}`)
    }

    // Fetch data from both sources
    const [sanityPlayers, csvPlayers] = await Promise.all([
      fetchSanityPlayers(),
      readCSVPlayers(CSV_FILE_PATH)
    ])

    // Perform comparison
    const comparison = comparePlayerLists(sanityPlayers, csvPlayers)

    // Display results
    displayResults(comparison)

    // Optional: Save results to a file
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
    const reportPath = `tools/comparison-report-${timestamp}.json`
    
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        totalSanity: comparison.totalSanity,
        totalCSV: comparison.totalCSV,
        commonPlayers: comparison.commonPlayers,
        onlyInSanity: comparison.inSanityNotInCSV.length,
        onlyInCSV: comparison.inCSVNotInSanity.length
      },
      playersOnlyInSanity: comparison.inSanityNotInCSV,
      playersOnlyInCSV: comparison.inCSVNotInSanity
    }

    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2))
    console.log(`\n📄 Detailed report saved to: ${reportPath}`)

  } catch (error) {
    console.error('\n❌ Error during comparison:', error.message)
    process.exit(1)
  }
}

// Run the script
if (require.main === module) {
  main()
}

module.exports = { fetchSanityPlayers, readCSVPlayers, comparePlayerLists }
