const { createClient } = require('@sanity/client')
require('dotenv').config()

// Configuration
const projectId = process.env.NEXT_PUBLIC_SANITY_PROJECT_ID || process.env.SANITY_PROJECT_ID
const dataset = process.env.NEXT_PUBLIC_SANITY_DATASET || process.env.SANITY_DATASET
const token = process.env.SANITY_TOKEN

if (!projectId || !dataset || !token) {
  console.error('Missing required environment variables:')
  console.error('- SANITY_PROJECT_ID or NEXT_PUBLIC_SANITY_PROJECT_ID')
  console.error('- SANITY_DATASET or NEXT_PUBLIC_SANITY_DATASET') 
  console.error('- SANITY_TOKEN')
  process.exit(1)
}

// Initialize Sanity client
const client = createClient({
  projectId,
  dataset,
  token,
  apiVersion: '2024-03-24',
  useCdn: false,
})

// Function to convert string to PortableText blocks
function stringToPortableText(text) {
  if (!text || typeof text !== 'string') {
    return []
  }
  
  // Split by line breaks and create paragraph blocks
  const paragraphs = text.split('\n').filter(p => p.trim().length > 0)
  
  return paragraphs.map(paragraph => ({
    _type: 'block',
    _key: Math.random().toString(36).substr(2, 9),
    style: 'normal',
    markDefs: [],
    children: [
      {
        _type: 'span',
        _key: Math.random().toString(36).substr(2, 9),
        text: paragraph.trim(),
        marks: []
      }
    ]
  }))
}

async function migratePartnerDescriptions() {
  try {
    console.log('🔍 Fetching partners with string descriptions...')
    
    // Fetch all partners
    const partners = await client.fetch(`
      *[_type == "partner" && !(_id in path("drafts.**"))] {
        _id,
        _rev,
        name,
        description
      }
    `)
    
    console.log(`📊 Found ${partners.length} partners`)
    
    // Filter partners that have string descriptions
    const partnersToMigrate = partners.filter(partner => 
      partner.description && typeof partner.description === 'string'
    )
    
    console.log(`🔄 ${partnersToMigrate.length} partners need migration`)
    
    if (partnersToMigrate.length === 0) {
      console.log('✅ No partners need migration!')
      return
    }
    
    // Show what will be migrated
    console.log('\n📋 Partners to migrate:')
    partnersToMigrate.forEach(partner => {
      console.log(`- ${partner.name}: "${partner.description.substring(0, 50)}${partner.description.length > 50 ? '...' : ''}"`)
    })
    
    // Ask for confirmation
    const readline = require('readline')
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    })
    
    const answer = await new Promise(resolve => {
      rl.question('\n❓ Do you want to proceed with the migration? (y/N): ', resolve)
    })
    rl.close()
    
    if (answer.toLowerCase() !== 'y' && answer.toLowerCase() !== 'yes') {
      console.log('❌ Migration cancelled')
      return
    }
    
    console.log('\n🚀 Starting migration...')
    
    // Migrate each partner
    for (const partner of partnersToMigrate) {
      try {
        const portableTextDescription = stringToPortableText(partner.description)
        
        console.log(`📝 Migrating ${partner.name}...`)
        
        await client
          .patch(partner._id)
          .set({ description: portableTextDescription })
          .commit()
        
        console.log(`✅ Successfully migrated ${partner.name}`)
      } catch (error) {
        console.error(`❌ Failed to migrate ${partner.name}:`, error.message)
      }
    }
    
    console.log('\n🎉 Migration completed!')
    console.log('💡 You can now use the rich text editor in Sanity Studio for partner descriptions.')
    
  } catch (error) {
    console.error('❌ Migration failed:', error.message)
    process.exit(1)
  }
}

// Run the migration
migratePartnerDescriptions()
