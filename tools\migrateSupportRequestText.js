const { createClient } = require('@sanity/client')
require('dotenv').config()

// Configuration
const projectId = process.env.NEXT_PUBLIC_SANITY_PROJECT_ID || process.env.SANITY_PROJECT_ID
const dataset = process.env.NEXT_PUBLIC_SANITY_DATASET || process.env.SANITY_DATASET
const token = process.env.SANITY_TOKEN

if (!projectId || !dataset || !token) {
  console.error('Missing required environment variables:')
  console.error('- SANITY_PROJECT_ID or NEXT_PUBLIC_SANITY_PROJECT_ID')
  console.error('- SANITY_DATASET or NEXT_PUBLIC_SANITY_DATASET') 
  console.error('- SANITY_TOKEN')
  process.exit(1)
}

// Initialize Sanity client
const client = createClient({
  projectId,
  dataset,
  token,
  apiVersion: '2024-03-24',
  useCdn: false,
})

// Function to convert string to PortableText blocks
function stringToPortableText(text) {
  if (!text || typeof text !== 'string') {
    return []
  }
  
  // Split by line breaks and create paragraph blocks
  const paragraphs = text.split('\n').filter(p => p.trim().length > 0)
  
  return paragraphs.map(paragraph => ({
    _type: 'block',
    _key: Math.random().toString(36).substr(2, 9),
    style: 'normal',
    markDefs: [],
    children: [
      {
        _type: 'span',
        _key: Math.random().toString(36).substr(2, 9),
        text: paragraph.trim(),
        marks: []
      }
    ]
  }))
}

async function migrateSupportRequestText() {
  try {
    console.log('🔍 Fetching support requests with string text fields...')
    
    // Fetch all support requests
    const requests = await client.fetch(`
      *[_type == "supportRequest" && !(_id in path("drafts.**"))] {
        _id,
        _rev,
        subject,
        message,
        adminNotes
      }
    `)
    
    console.log(`📊 Found ${requests.length} support requests`)
    
    // Filter requests that have string messages or adminNotes
    const requestsToMigrate = requests.filter(request => 
      (request.message && typeof request.message === 'string') ||
      (request.adminNotes && typeof request.adminNotes === 'string')
    )
    
    console.log(`🔄 ${requestsToMigrate.length} support requests need migration`)
    
    if (requestsToMigrate.length === 0) {
      console.log('✅ No support requests need migration!')
      return
    }
    
    // Show what will be migrated
    console.log('\n📋 Support requests to migrate:')
    requestsToMigrate.forEach(request => {
      const messagePreview = request.message && typeof request.message === 'string' 
        ? `Message: "${request.message.substring(0, 30)}${request.message.length > 30 ? '...' : ''}"`
        : ''
      const notesPreview = request.adminNotes && typeof request.adminNotes === 'string'
        ? `Notes: "${request.adminNotes.substring(0, 30)}${request.adminNotes.length > 30 ? '...' : ''}"`
        : ''
      console.log(`- ${request.subject}: ${messagePreview} ${notesPreview}`)
    })
    
    // Ask for confirmation
    const readline = require('readline')
    const rl = readline.createInterface({
      input: process.stdin,
      output: process.stdout
    })
    
    const answer = await new Promise(resolve => {
      rl.question('\n❓ Do you want to proceed with the migration? (y/N): ', resolve)
    })
    rl.close()
    
    if (answer.toLowerCase() !== 'y' && answer.toLowerCase() !== 'yes') {
      console.log('❌ Migration cancelled')
      return
    }
    
    console.log('\n🚀 Starting migration...')
    
    // Migrate each support request
    for (const request of requestsToMigrate) {
      try {
        const updates = {}
        
        if (request.message && typeof request.message === 'string') {
          updates.message = stringToPortableText(request.message)
        }
        
        if (request.adminNotes && typeof request.adminNotes === 'string') {
          updates.adminNotes = stringToPortableText(request.adminNotes)
        }
        
        console.log(`📝 Migrating ${request.subject}...`)
        
        await client
          .patch(request._id)
          .set(updates)
          .commit()
        
        console.log(`✅ Successfully migrated ${request.subject}`)
      } catch (error) {
        console.error(`❌ Failed to migrate ${request.subject}:`, error.message)
      }
    }
    
    console.log('\n🎉 Migration completed!')
    console.log('💡 You can now use the rich text editor in Sanity Studio for support request messages and admin notes.')
    
  } catch (error) {
    console.error('❌ Migration failed:', error.message)
    process.exit(1)
  }
}

// Run the migration
migrateSupportRequestText()
