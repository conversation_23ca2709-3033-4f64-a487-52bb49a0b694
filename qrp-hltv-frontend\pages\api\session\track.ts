import type { NextApiRequest, NextApiResponse } from 'next'
import { createSession, updateSessionActivity, logActivity } from '../../../lib/sessionUtils'

function getClientIP(req: NextApiRequest): string {
  const forwarded = req.headers['x-forwarded-for']
  const realIP = req.headers['x-real-ip']
  
  if (forwarded) {
    return Array.isArray(forwarded) ? forwarded[0] : forwarded.split(',')[0]
  }
  
  if (realIP) {
    return Array.isArray(realIP) ? realIP[0] : realIP
  }
  
  return req.socket.remoteAddress || '127.0.0.1'
}

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse
) {
  if (req.method !== 'POST') {
    return res.status(405).json({ message: 'Method not allowed' })
  }

  try {
    const { 
      sessionId, 
      page, 
      pageTitle, 
      activityType = 'page_view',
      referrer,
      element,
      elementText,
      targetUrl,
      scrollDepth,
      timeOnPage,
      searchQuery,
      filterValues,
      formData,
      metadata
    } = req.body

    const ipAddress = getClientIP(req)
    const userAgent = req.headers['user-agent'] || 'Unknown'

    // If no sessionId provided, create a new session
    if (!sessionId) {
      const newSessionId = await createSession(
        ipAddress,
        userAgent,
        page,
        referrer
      )
      
      return res.status(200).json({ 
        success: true, 
        sessionId: newSessionId,
        message: 'New session created'
      })
    }

    // Update existing session activity
    if (activityType === 'page_view') {
      await updateSessionActivity(sessionId, page, pageTitle)
    } else {
      // Log other types of activities
      await logActivity({
        sessionId,
        timestamp: new Date().toISOString(),
        activityType,
        page,
        pageTitle,
        element,
        elementText,
        targetUrl,
        scrollDepth,
        timeOnPage,
        searchQuery,
        filterValues,
        formData,
        metadata
      })
    }

    return res.status(200).json({ 
      success: true, 
      message: 'Activity tracked successfully'
    })

  } catch (error) {
    console.error('Error tracking session:', error)
    return res.status(500).json({ 
      success: false, 
      message: 'Internal server error' 
    })
  }
}
