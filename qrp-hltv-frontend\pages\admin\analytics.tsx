import { useState, useEffect } from 'react'
import Head from 'next/head'
import { useLanguage } from '../../lib/LanguageContext'
import { useRouter } from 'next/router'

interface SessionData {
  sessionId: string
  ipAddress: string
  userAgent: string
  startTime: string
  lastActivity: string
  endTime?: string
  isActive: boolean
  country?: string
  countryCode?: string
  region?: string
  city?: string
  timezone?: string
  isp?: string
  pageViews: number
  duration?: number
  referrer?: string
  entryPage: string
  exitPage?: string
}

interface ActivityData {
  sessionId: string
  timestamp: string
  activityType: string
  page: string
  pageTitle?: string
  element?: string
  elementText?: string
  targetUrl?: string
  scrollDepth?: number
  timeOnPage?: number
  searchQuery?: string
  filterValues?: string[]
  formData?: any
  metadata?: any
}

interface AnalyticsData {
  success: boolean
  hasAccess: boolean
  adminUser?: {
    nickname: string
    role: string
  }
  metrics: {
    activeSessionsCount: number
    totalSessions: number
    activeSessions: number
    avgSessionDuration: number
    totalActivities: number
  }
  sessions: SessionData[]
  activities: ActivityData[]
  analytics: {
    topPages: { page: string; views: number }[]
    topCountries: { country: string; sessions: number }[]
    activityTypeStats: Record<string, number>
  }
  timestamp: string
}

export default function AdminAnalytics() {
  const { t } = useLanguage()
  const router = useRouter()
  const [data, setData] = useState<AnalyticsData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [adminCredentials, setAdminCredentials] = useState({ playerId: '', discordTag: '' })
  const [isAuthenticated, setIsAuthenticated] = useState(false)

  const fetchAnalytics = async () => {
    if (!adminCredentials.playerId && !adminCredentials.discordTag) return

    try {
      const params = new URLSearchParams()
      if (adminCredentials.playerId) params.append('playerId', adminCredentials.playerId)
      if (adminCredentials.discordTag) params.append('discordTag', adminCredentials.discordTag)

      const response = await fetch(`/api/admin/analytics?${params}`)
      const result = await response.json()

      if (result.hasAccess) {
        setData(result)
        setIsAuthenticated(true)
        setError(null)
      } else {
        setError('Access denied. Admin privileges required.')
        setIsAuthenticated(false)
      }
    } catch (err) {
      setError('Failed to fetch analytics data')
      console.error('Analytics fetch error:', err)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (isAuthenticated) {
      fetchAnalytics()
      
      // Set up polling for real-time updates
      const interval = setInterval(fetchAnalytics, 10000) // Update every 10 seconds
      return () => clearInterval(interval)
    }
  }, [isAuthenticated, adminCredentials])

  const handleLogin = (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    fetchAnalytics()
  }

  const formatDuration = (seconds: number) => {
    const hours = Math.floor(seconds / 3600)
    const minutes = Math.floor((seconds % 3600) / 60)
    const secs = seconds % 60

    if (hours > 0) {
      return `${hours}h ${minutes}m ${secs}s`
    } else if (minutes > 0) {
      return `${minutes}m ${secs}s`
    } else {
      return `${secs}s`
    }
  }

  const formatTimeAgo = (timestamp: string) => {
    const now = new Date()
    const time = new Date(timestamp)
    const diffMs = now.getTime() - time.getTime()
    const diffMins = Math.floor(diffMs / 60000)
    const diffHours = Math.floor(diffMins / 60)
    const diffDays = Math.floor(diffHours / 24)

    if (diffDays > 0) return `${diffDays}d ago`
    if (diffHours > 0) return `${diffHours}h ago`
    if (diffMins > 0) return `${diffMins}m ago`
    return 'Just now'
  }

  const getActivityIcon = (activityType: string) => {
    const icons: Record<string, string> = {
      page_view: '👁️',
      click: '👆',
      scroll: '📜',
      form_submit: '📝',
      modal_open: '🔍',
      modal_close: '❌',
      search: '🔍',
      filter: '🔽',
      download: '⬇️',
      external_link: '🔗',
      session_start: '🟢',
      session_end: '🔴'
    }
    return icons[activityType] || '📊'
  }

  if (!isAuthenticated) {
    return (
      <>
        <Head>
          <title>Admin Analytics - QRP HLTV</title>
          <meta name="robots" content="noindex, nofollow" />
        </Head>

        <div className="min-h-screen bg-zinc-950 flex items-center justify-center px-4">
          <div className="max-w-md w-full">
            <div className="bg-zinc-900 border border-zinc-800 rounded-xl p-8">
              <div className="text-center mb-8">
                <h1 className="text-2xl font-bold text-zinc-100 mb-2">Admin Analytics</h1>
                <p className="text-zinc-400">Enter your admin credentials to access real-time analytics</p>
              </div>

              <form onSubmit={handleLogin} className="space-y-6">
                <div>
                  <label className="block text-sm font-medium text-zinc-300 mb-2">
                    Player ID (optional)
                  </label>
                  <input
                    type="text"
                    value={adminCredentials.playerId}
                    onChange={(e) => setAdminCredentials(prev => ({ ...prev, playerId: e.target.value }))}
                    className="w-full px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-lg text-zinc-100 focus:outline-none focus:border-purple-500"
                    placeholder="Enter your player ID"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-zinc-300 mb-2">
                    Discord Tag (optional)
                  </label>
                  <input
                    type="text"
                    value={adminCredentials.discordTag}
                    onChange={(e) => setAdminCredentials(prev => ({ ...prev, discordTag: e.target.value }))}
                    className="w-full px-3 py-2 bg-zinc-800 border border-zinc-700 rounded-lg text-zinc-100 focus:outline-none focus:border-purple-500"
                    placeholder="@username"
                  />
                </div>

                {error && (
                  <div className="bg-red-900/20 border border-red-800 rounded-lg p-3">
                    <p className="text-red-400 text-sm">{error}</p>
                  </div>
                )}

                <button
                  type="submit"
                  disabled={loading || (!adminCredentials.playerId && !adminCredentials.discordTag)}
                  className="w-full bg-purple-600 hover:bg-purple-700 disabled:bg-zinc-700 disabled:cursor-not-allowed text-white font-medium py-2 px-4 rounded-lg transition"
                >
                  {loading ? 'Verifying...' : 'Access Analytics'}
                </button>
              </form>

              <div className="mt-6 text-center">
                <button
                  onClick={() => router.push('/admin')}
                  className="text-zinc-400 hover:text-zinc-300 text-sm transition"
                >
                  ← Back to Admin Page
                </button>
              </div>
            </div>
          </div>
        </div>
      </>
    )
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-zinc-950 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-purple-500 mx-auto mb-4"></div>
          <p className="text-zinc-400">Loading analytics...</p>
        </div>
      </div>
    )
  }

  return (
    <>
      <Head>
        <title>Admin Analytics - QRP HLTV</title>
        <meta name="robots" content="noindex, nofollow" />
      </Head>

      <div className="min-h-screen bg-zinc-950 text-zinc-100">
        <div className="max-w-7xl mx-auto px-4 py-8">
          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <div>
              <h1 className="text-3xl font-bold text-zinc-100 mb-2">Real-time Analytics</h1>
              <p className="text-zinc-400">
                Welcome, <span className="text-purple-400">{data?.adminUser?.nickname}</span> 
                <span className="ml-2 px-2 py-1 bg-purple-900/30 text-purple-300 rounded text-xs">
                  {data?.adminUser?.role}
                </span>
              </p>
            </div>
            <div className="text-right">
              <p className="text-sm text-zinc-400">Last updated</p>
              <p className="text-sm text-zinc-300">{data?.timestamp ? formatTimeAgo(data.timestamp) : 'Never'}</p>
            </div>
          </div>

          {/* Metrics Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6 mb-8">
            <div className="bg-zinc-900 border border-zinc-800 rounded-xl p-6">
              <div className="flex items-center">
                <div className="p-2 bg-green-900/30 rounded-lg">
                  <span className="text-2xl">🟢</span>
                </div>
                <div className="ml-4">
                  <p className="text-sm text-zinc-400">Active Users</p>
                  <p className="text-2xl font-bold text-green-400">{data?.metrics.activeSessionsCount || 0}</p>
                </div>
              </div>
            </div>

            <div className="bg-zinc-900 border border-zinc-800 rounded-xl p-6">
              <div className="flex items-center">
                <div className="p-2 bg-blue-900/30 rounded-lg">
                  <span className="text-2xl">👥</span>
                </div>
                <div className="ml-4">
                  <p className="text-sm text-zinc-400">Total Sessions</p>
                  <p className="text-2xl font-bold text-blue-400">{data?.metrics.totalSessions || 0}</p>
                </div>
              </div>
            </div>

            <div className="bg-zinc-900 border border-zinc-800 rounded-xl p-6">
              <div className="flex items-center">
                <div className="p-2 bg-purple-900/30 rounded-lg">
                  <span className="text-2xl">⏱️</span>
                </div>
                <div className="ml-4">
                  <p className="text-sm text-zinc-400">Avg Duration</p>
                  <p className="text-2xl font-bold text-purple-400">
                    {data?.metrics.avgSessionDuration ? formatDuration(data.metrics.avgSessionDuration) : '0s'}
                  </p>
                </div>
              </div>
            </div>

            <div className="bg-zinc-900 border border-zinc-800 rounded-xl p-6">
              <div className="flex items-center">
                <div className="p-2 bg-orange-900/30 rounded-lg">
                  <span className="text-2xl">📊</span>
                </div>
                <div className="ml-4">
                  <p className="text-sm text-zinc-400">Activities</p>
                  <p className="text-2xl font-bold text-orange-400">{data?.metrics.totalActivities || 0}</p>
                </div>
              </div>
            </div>

            <div className="bg-zinc-900 border border-zinc-800 rounded-xl p-6">
              <div className="flex items-center">
                <div className="p-2 bg-yellow-900/30 rounded-lg">
                  <span className="text-2xl">🔄</span>
                </div>
                <div className="ml-4">
                  <p className="text-sm text-zinc-400">Active Now</p>
                  <p className="text-2xl font-bold text-yellow-400">{data?.metrics.activeSessions || 0}</p>
                </div>
              </div>
            </div>
          </div>

          {/* Main Content Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
            {/* Active Sessions */}
            <div className="bg-zinc-900 border border-zinc-800 rounded-xl p-6">
              <h2 className="text-xl font-bold text-zinc-100 mb-4">Active Sessions</h2>
              <div className="space-y-3 max-h-96 overflow-y-auto">
                {data?.sessions.filter(s => s.isActive).map((session) => (
                  <div key={session.sessionId} className="bg-zinc-800 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center">
                        <span className="w-2 h-2 bg-green-400 rounded-full mr-2"></span>
                        <span className="text-sm font-mono text-zinc-300">
                          {session.sessionId.substring(0, 8)}...
                        </span>
                      </div>
                      <span className="text-xs text-zinc-400">
                        {formatTimeAgo(session.lastActivity)}
                      </span>
                    </div>
                    <div className="grid grid-cols-2 gap-2 text-xs text-zinc-400">
                      <div>IP: {session.ipAddress}</div>
                      <div>Views: {session.pageViews}</div>
                      <div>Location: {session.city || 'Unknown'}</div>
                      <div>Entry: {session.entryPage}</div>
                    </div>
                  </div>
                ))}
                {data?.sessions.filter(s => s.isActive).length === 0 && (
                  <p className="text-zinc-400 text-center py-8">No active sessions</p>
                )}
              </div>
            </div>

            {/* Live Activity Feed */}
            <div className="bg-zinc-900 border border-zinc-800 rounded-xl p-6">
              <h2 className="text-xl font-bold text-zinc-100 mb-4">Live Activity Feed</h2>
              <div className="space-y-2 max-h-96 overflow-y-auto">
                {data?.activities.slice(0, 20).map((activity, index) => (
                  <div key={index} className="flex items-center space-x-3 py-2 border-b border-zinc-800 last:border-b-0">
                    <span className="text-lg">{getActivityIcon(activity.activityType)}</span>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center space-x-2">
                        <span className="text-sm font-medium text-zinc-300">
                          {activity.activityType.replace('_', ' ')}
                        </span>
                        <span className="text-xs text-zinc-500">
                          {activity.sessionId.substring(0, 6)}...
                        </span>
                      </div>
                      <p className="text-xs text-zinc-400 truncate">
                        {activity.page} {activity.elementText && `• ${activity.elementText.substring(0, 30)}...`}
                      </p>
                    </div>
                    <span className="text-xs text-zinc-500 whitespace-nowrap">
                      {formatTimeAgo(activity.timestamp)}
                    </span>
                  </div>
                ))}
                {data?.activities.length === 0 && (
                  <p className="text-zinc-400 text-center py-8">No recent activities</p>
                )}
              </div>
            </div>
          </div>

          {/* Analytics Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Top Pages */}
            <div className="bg-zinc-900 border border-zinc-800 rounded-xl p-6">
              <h2 className="text-xl font-bold text-zinc-100 mb-4">Top Pages</h2>
              <div className="space-y-3">
                {data?.analytics.topPages.slice(0, 10).map((page, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <div className="flex-1 min-w-0">
                      <p className="text-sm text-zinc-300 truncate">{page.page}</p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-16 bg-zinc-800 rounded-full h-2">
                        <div
                          className="bg-purple-500 h-2 rounded-full"
                          style={{
                            width: `${Math.min(100, (page.views / (data?.analytics.topPages[0]?.views || 1)) * 100)}%`
                          }}
                        ></div>
                      </div>
                      <span className="text-xs text-zinc-400 w-8 text-right">{page.views}</span>
                    </div>
                  </div>
                ))}
                {data?.analytics.topPages.length === 0 && (
                  <p className="text-zinc-400 text-center py-8">No page data</p>
                )}
              </div>
            </div>

            {/* Top Countries */}
            <div className="bg-zinc-900 border border-zinc-800 rounded-xl p-6">
              <h2 className="text-xl font-bold text-zinc-100 mb-4">Top Countries</h2>
              <div className="space-y-3">
                {data?.analytics.topCountries.slice(0, 10).map((country, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <div className="flex-1 min-w-0">
                      <p className="text-sm text-zinc-300 truncate">{country.country}</p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-16 bg-zinc-800 rounded-full h-2">
                        <div
                          className="bg-blue-500 h-2 rounded-full"
                          style={{
                            width: `${Math.min(100, (country.sessions / (data?.analytics.topCountries[0]?.sessions || 1)) * 100)}%`
                          }}
                        ></div>
                      </div>
                      <span className="text-xs text-zinc-400 w-8 text-right">{country.sessions}</span>
                    </div>
                  </div>
                ))}
                {data?.analytics.topCountries.length === 0 && (
                  <p className="text-zinc-400 text-center py-8">No location data</p>
                )}
              </div>
            </div>

            {/* Activity Types */}
            <div className="bg-zinc-900 border border-zinc-800 rounded-xl p-6">
              <h2 className="text-xl font-bold text-zinc-100 mb-4">Activity Types</h2>
              <div className="space-y-3">
                {Object.entries(data?.analytics.activityTypeStats || {})
                  .sort(([,a], [,b]) => b - a)
                  .slice(0, 10)
                  .map(([type, count], index) => (
                  <div key={index} className="flex items-center justify-between">
                    <div className="flex items-center space-x-2 flex-1 min-w-0">
                      <span className="text-sm">{getActivityIcon(type)}</span>
                      <p className="text-sm text-zinc-300 truncate">{type.replace('_', ' ')}</p>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-16 bg-zinc-800 rounded-full h-2">
                        <div
                          className="bg-green-500 h-2 rounded-full"
                          style={{
                            width: `${Math.min(100, (count / Math.max(...Object.values(data?.analytics.activityTypeStats || {}))) * 100)}%`
                          }}
                        ></div>
                      </div>
                      <span className="text-xs text-zinc-400 w-8 text-right">{count}</span>
                    </div>
                  </div>
                ))}
                {Object.keys(data?.analytics.activityTypeStats || {}).length === 0 && (
                  <p className="text-zinc-400 text-center py-8">No activity data</p>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}
