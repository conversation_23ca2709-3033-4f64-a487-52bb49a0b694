// Check all recent support requests to see their message format
const { createClient } = require('@sanity/client')

const client = createClient({
  projectId: 'al3wd5y8',
  dataset: 'production',
  useCdn: false,
  apiVersion: '2023-05-03',
  token: process.env.SANITY_API_TOKEN,
})

async function checkAllRecentSupportRequests() {
  try {
    console.log('🔍 Fetching all recent support requests...')
    
    // Fetch the 10 most recent support requests
    const requests = await client.fetch(`
      *[_type == "supportRequest" && !(_id in path("drafts.**"))] | order(_createdAt desc)[0...10] {
        _id,
        _createdAt,
        subject,
        message,
        name,
        discord
      }
    `)
    
    if (!requests || requests.length === 0) {
      console.log('❌ No support requests found')
      return
    }
    
    console.log(`📋 Found ${requests.length} recent support requests:\n`)
    
    requests.forEach((request, index) => {
      console.log(`${index + 1}. Support Request:`)
      console.log(`   - ID: ${request._id}`)
      console.log(`   - Subject: ${request.subject}`)
      console.log(`   - From: ${request.name} (${request.discord})`)
      console.log(`   - Created: ${request._createdAt}`)
      console.log(`   - Message type: ${typeof request.message}`)
      console.log(`   - Message is array: ${Array.isArray(request.message)}`)
      
      if (Array.isArray(request.message)) {
        console.log(`   ✅ PROPERLY FORMATTED as rich text`)
        console.log(`   - Number of blocks: ${request.message.length}`)
        if (request.message.length > 0 && request.message[0].children && request.message[0].children.length > 0) {
          const text = request.message[0].children[0].text
          const preview = text.length > 50 ? text.substring(0, 50) + '...' : text
          console.log(`   - Text preview: "${preview}"`)
        }
      } else {
        console.log(`   ❌ STILL STRING FORMAT: "${request.message}"`)
      }
      console.log('')
    })
    
  } catch (error) {
    console.error('❌ Check failed:', error.message)
  }
}

// Run the check
checkAllRecentSupportRequests()
